import React, {useState, useRef, useContext, useEffect} from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  MenuItem,
  Select,
  Button,
  InputLabel,
  TextField,
  Box,
  FormControl,
  IconButton,
  Autocomplete,
  CircularProgress,
  Typography,
  FormControlLabel,
  Radio,
  RadioGroup,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import {DropzoneArea} from "material-ui-dropzone";
import {ButtonBasic} from "../../components/buttons/Buttons";
import {
  toastMessage,
  toastMessageSuccess,
  toastMessageWarning,
} from "../../tools/toast";
import {HashtagContext} from "../../services2/hashtag/hashtag.context";
import {convertBase64} from "../../hooks/useBase64";
import axios from "axios";
import {dbConfig} from "../../infrastructure/db/db-config";
import LoggingFunction from "../../components/CFR-Report/LoggingFunction";
import GetPreviewComponent from "../../components/commons/getPreview.component";
import {useAuth} from "../../hooks/AuthProvider";
import GalleryModal from "./GalleryModal";
import CloseIcon from '@mui/icons-material/Close';

const AddStepDialog = ({
  open,
  setOpen,
  data,
  machinetype,
  manual_id,
  alarm_sop_id,
  machineName,
  setRefreshCount,
  refreshCount,
  currentMode,
  parent,
  handleHastageData,
  setTempHashes,
  hashArray,
  temptemp,
  currentStepIndex,
}) => {
  const [title, setTitle] = useState("");
  const [sensorValue, setSensorValue] = useState("");
  const [sensorList, setSensorList] = useState([]);
  const [sensor, setSensor] = useState([]);
  const [qrcodeValue, setQrcodeValue] = useState("");
  const [qrcodes, setQrcodes] = useState([]);
  const [desc, setDesc] = useState("");
  const [ans, setAns] = useState("");
  const [type, setType] = useState("");
  const [format, setFormat] = useState("");
  const [hashtag, setHashtag] = useState("");
  const [hashdata, sethashdata] = useState("");
  const [hashtagArray, setHashtagArray] = useState([]);
  const [Change, setChange] = useState(false);
  const [selectedFileForStorage, setSelectedFileForStorage] = useState(null);
  const [imageUrl, setImageUrl] = useState("");
  const [file, setFile] = useState(null);
  const [findBy, setFindBy] = useState("");
  const [inputFindBy, setInputFindBy] = useState("");
  const searchInput = useRef(null);
  const [loading, setLoading] = useState(false);
  const {hashes} = useContext(HashtagContext);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [steps, setSteps] = useState([]);
  const [insertIndex, setInsertIndex] = useState("");
  const justOpened = useRef(false);
  const [fileManagerFiles, setFileManagerFiles] = useState([]);
  const [folderId, setFolderId] = useState([]);
  const [selectedFileUrl, setSelectedFileUrl] = useState("");
  const [mediaSource, setMediaSource] = useState("upload");
  const [galleryModalOpen, setGalleryModalOpen] = useState(false);
  const {currentUser} = useAuth();
  const mid = data;

  const menuItemTheme = {
    backgroundColor: currentMode === "Dark" ? "#161C24" : "#fff",
  };

  // Define supported MIME types
  const typesImages = ["image/png", "image/jpeg", "image/jpg"];
  const videoTypes = ["video/mp4", "video/mkv", "video/mov"];
  const audioTypes = ["audio/mp3", "audio/mpeg"];

  // Map file_type to MIME types for filtering
  const fileTypeToMime = {
    image: typesImages,
    video: videoTypes,
    audio: audioTypes,
  };

  // Fetch sensors
  const getSensorData = async () => {
    try {
      const response = await axios.get(
        `${dbConfig.url}/livedata/getFromMachine/${data}`,
      );
      setSensorList(response?.data?.data || []);
    } catch (error) {
      console.error("Error fetching sensor data:", error);
      toastMessage({message: "Failed to fetch sensor data"});
    }
  };

  // Fetch steps for the current manual_id
  const getStepData = async () => {
    if (!manual_id) return;
    try {
      const response = await axios.get(
        `${dbConfig.url}/stepdata/getManualSteps/${manual_id}`,
      );
      setSteps(response?.data?.data || []);
    } catch (error) {
      setSteps([]);
      console.error("Error fetching step data:", error);
    }
  };

  // Fetch alarm SOP steps for the current alarm_sop_id
  const getAlarmSopStepData = async () => {
    try {
      const alarmSopStepsResponse = await axios.get(
        `${dbConfig.url}/alarmSopStepData/getAlarmSOPSteps/${alarm_sop_id}`,
      );
      setSteps(alarmSopStepsResponse?.data?.data || []);
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("Error fetching alarm SOP step data:", error);
      }
      setSteps([]);
    }
  };

  // Fetch files from file manager API
  const getFileManagerFiles = async () => {
    try {
      const response = await axios.get(`${dbConfig.url}/files`);
      console.log("File manager API response:", response.data);
      const files = response?.data?.data || [];
      setFileManagerFiles(files);
      console.log("fileManagerFiles:", files);
    } catch (error) {
      console.error("Error fetching file manager files:", error);
      toastMessage({message: "Failed to fetch files from file manager"});
      setFileManagerFiles([]);
    }
  };

  const getFolderData = async () => {
    try {
      const response = await axios.get(`${dbConfig?.url}/folders`);
      const folder = response?.data?.data || [];
      setFolderId(folder);
      console.log("Folder", folder);
    } catch (error) {
      console.error("Error fetching file manager files:", error);
      toastMessage({message: "Failed to fetch files from file manager"});
      setFolderId([]);
    }
  };

  useEffect(() => {
    if (open) {
      justOpened.current = true;
      if (data) getSensorData();
      if (manual_id && parent !== "Alarm SOP") getStepData();

      if (alarm_sop_id && parent === "Alarm SOP") {
        getAlarmSopStepData();
      }

      getFileManagerFiles();
      getFolderData();
    }
  }, [open, data, manual_id, alarm_sop_id]);

  useEffect(() => {
    if (open && justOpened.current) {
      if (
        typeof currentStepIndex === "number" &&
        !isNaN(currentStepIndex) &&
        steps &&
        steps.length > 0
      ) {
        setInsertIndex(String(currentStepIndex + 2));
      } else {
        setInsertIndex("1");
      }
      justOpened.current = false;
    }
  }, [open, steps, currentStepIndex]);

  const handleClose = () => {
    setOpen(false);
    setTitle("");
    setDesc("");
    setAns("");
    setSensor([]);
    setSensorValue("");
    setQrcodeValue("");
    setQrcodes([]);
    setFile(null);
    setHashtag("");
    setChange(false);
    setImageUrl("");
    setFormat("");
    setHashtagArray([]);
    setFindBy("");
    setSelectedFileForStorage(null);
    setLoading(false);
    setInsertIndex("");
    setSelectedFileUrl("");
    setMediaSource("upload");
  };

  const targetFolder = folderId?.find(folder => {
    return folder.mid === data;
  });

  const parentId = targetFolder?._id || folderId?.[0]?._id || null; // Fallback to first folder or null

  const handleSubmit = async () => {
    setLoading(true);
    try {
      let sortKey = 100;
      let index = steps?.length || 0;

      if (steps && steps.length > 0 && insertIndex) {
        const idx = Number(insertIndex);
        if (isNaN(idx) || idx < 1 || idx > steps.length + 1) {
          toastMessage({message: "Invalid insert index"});
          setLoading(false);
          return;
        }
        const sorted = [...steps].sort(
          (a, b) => (a.sortKey ?? 0) - (b.sortKey ?? 0),
        );
        if (idx === 1) {
          const firstSortKey = sorted[0]?.sortKey ?? 100;
          sortKey = Math.floor(firstSortKey / 2);
        } else if (idx === sorted.length + 1) {
          sortKey = (sorted[sorted.length - 1]?.sortKey ?? 100) + 100;
        } else {
          const left = sorted[idx - 2];
          const right = sorted[idx - 1];
          sortKey = Math.floor(
            ((left?.sortKey ?? 0) + (right?.sortKey ?? 0)) / 2,
          );
        }
        index = idx - 1;
      }

      let data = {
        title,
        desc,
        manual_id,
        index,
        sortKey,
        type,
        format,
        sub_steps: false,
        sensor,
        ans,
        qr_codes: qrcodes,
      };

      if (format !== "text") {
        if (mediaSource === "fileManager" && selectedFileUrl) {
          data = {...data, url: selectedFileUrl};
        } else if (mediaSource === "upload" && selectedFileForStorage) {
          let fd = new FormData();
          fd.append("image", selectedFileForStorage);

          const resTemp = await axios
            .post(`${dbConfig?.url_storage}/upload`, fd, {
              onUploadProgress: progressEvent => {
                const percentCompleted = Math.round(
                  (progressEvent.loaded * 100) / progressEvent.total,
                );
                setUploadProgress(percentCompleted);
              },
            })
            .catch(err => {
              console.error("Storage error:", err);
              toastMessage({message: "Failed to upload file"});
              return null;
            });

          if (resTemp?.data?.data) {
            const uploadedUrl = resTemp.data.data;
          const fileType = selectedFileForStorage.type.split("/")[0]; // <-- gets correct format
             data = {...data, url: uploadedUrl, format: fileType}; // <-- update format here

            // ✅ Sync uploaded file to File Manager
            try {
              console.log("Syncing uploaded file to File Manager...", data?.format);
            const res = await axios.post(`${dbConfig.url}/files`, {
                url: uploadedUrl,
                name: selectedFileForStorage.name,
                creator: currentUser?.email,
                parent_id: parentId,
                file_type: fileType,
                mid: mid,
              });
            console.log("File synced successfully with File Manager", res);
            } catch (fileManagerError) {
              console.warn(
                "Uploaded to storage, but failed to sync with File Manager:",
                fileManagerError,
              );
            }
          } else {
            setLoading(false);
            return;
          }
        } else {
          toastMessage({message: "Please select or upload a media file"});
          setLoading(false);
          return;
        }
      }

      if (parent === "Maintenance" && parent !== "Alarm SOP") {
        const stepResponse = await axios.post(`${dbConfig.url}/stepdata`, data);
        const stepId = stepResponse?.data?.data?._id;

        if (!stepId) {
          toastMessage({message: "Failed to create step data"});
          setLoading(false);
          return;
        }

        const postRequests = hashtagArray?.map(hash =>
          axios.post(`${dbConfig.url}/hashtags`, {
            maintenance_id: manual_id,
            step_id: stepId,
            title: hash.trim(),
            type: machinetype,
          }),
        );

        try {
          const hashtagResponses = await Promise.all(postRequests);
          const hashtagIds = hashtagResponses
            .map(res => res?.data?.data?._id)
            .filter(Boolean);

          const updatedData = {...data, hashtag: hashtagIds};
          await axios.put(`${dbConfig.url}/stepdata/${stepId}`, updatedData);

          toastMessageSuccess({
            message: "Step and Hashtags Added Successfully",
          });
          handleHastageData();
          setHashtagArray([]);
          setRefreshCount(refreshCount + 1);
          LoggingFunction(
            machineName,
            parent,
            data?.title,
            parent || "Training",
            `New Step is added to ${data?.title}`,
          );
        } catch (error) {
          console.error("Error in hashtag creation or step update:", error);
          toastMessage({message: "Failed to add hashtags or update step"});
          setLoading(false);
          return;
        }
      } else if (parent !== "Alarm SOP") {
        await axios.post(`${dbConfig.url}/stepdata`, data);
        toastMessageSuccess({message: "Step Added Successfully"});
        setRefreshCount(refreshCount + 1);
        LoggingFunction(
          machineName,
          data?.title,
          parent,
          parent || "Training",
          `New Step is added to ${data?.title}`,
        );
      } else {
        const {manual_id, ...dataWithoutManualId} = data;
        const alarmSOPStepPayload = {...dataWithoutManualId, alarm_sop_id};
        await axios.post(
          `${dbConfig.url}/alarmSopStepData`,
          alarmSOPStepPayload,
        );
        toastMessageSuccess({message: "Step Added Successfully"});
        setRefreshCount(refreshCount + 1);
        LoggingFunction(
          machineName,
          data?.title,
          parent,
          parent || "Training",
          `New Step is added to ${data?.title}`,
        );
      }

      handleClose();
    } catch (error) {
      console.error("Error in handleSubmit:", error);
      toastMessage({message: "An error occurred while adding the step"});
    } finally {
      setLoading(false);
    }
  };

  const prePopDataSensor = idx => {
    let temp = [...sensor];
    temp.splice(idx, 1);
    setSensor(temp);
  };

  const prePopDataQr = idx => {
    let temp = [...qrcodes];
    temp.splice(idx, 1);
    setQrcodes(temp);
  };

  const handleButtonClick = () => {
    if (!hashtag?.trim()) {
      return toastMessageWarning({
        message: "Enter a hashtag",
        type: "warning",
      });
    }

    if (hashtag?.trim()) {
      setHashtagArray(prev => [...prev, hashtag]);
      setHashtag("");
      setChange(true);
    }
  };

  const handleButtonClickOld = () => {
    if (!findBy?.trim()) {
      return toastMessageWarning({
        message: "Select a hashtag to continue",
        type: "warning",
      });
    }

    setHashtagArray(prev => [...prev, findBy]);
    setChange(true);
    setFindBy("");
    sethashdata("");
  };

  const removeHashtag = index => {
    setHashtagArray(hashtagArray.filter((_, i) => i !== index));
    setChange(false);
  };

  const handleChanges = target => {
    setInputFindBy(target);
    setFindBy(target);
    const arr = temptemp?.filter(opt =>
      opt?.id.toLowerCase().includes(target?.toLowerCase()),
    );
    if (target?.trim()) setTempHashes([...arr]);
    else setTempHashes(temptemp);
    sethashdata(arr[0]?.id);
  };

  const handleChange = async loadedFiles => {
    const selectedFile = loadedFiles[0];
    if (!selectedFile) return;

    if (selectedFile.size > 50 * 1024 * 1024) {
      toastMessage({
        message: "File size exceeds 50MB. Please upload a smaller file.",
      });
      return;
    }

    const base64 = await convertBase64(selectedFile);
    setSelectedFileForStorage(selectedFile);

    if (!type || !format) {
      toastMessage({
        message: "Please Select a Type / Format first to proceed",
      });
      return;
    }

    if (format === "image" && typesImages.includes(selectedFile.type)) {
      setFile(selectedFile);
      setImageUrl(base64);
    } else if (format === "video" && videoTypes.includes(selectedFile.type)) {
      setFile(selectedFile);
      setImageUrl(base64);
    } else if (format === "audio" && audioTypes.includes(selectedFile.type)) {
      setFile(selectedFile);
      setImageUrl(base64);
    } else {
      setFile(null);
      toastMessage({
        message: "Invalid file type. Please upload a valid format.",
      });
    }
  };

  const handleDeleteMedia = () => {
    setImageUrl(null);
    setSelectedFileForStorage(null);
    setSelectedFileUrl("");
  };

  const handleFileManagerSelect = async file => {
    if (file && file.url) {
      try {
        // Construct the full URL - replace with your actual base URL
        const baseUrl = `${dbConfig.url_storage}/images/`; // or wherever your images are stored
        const fullUrl = file.url.startsWith("http")
          ? file.url
          : `${baseUrl}${file.url}`;

        console.log("Fetching URL:", fullUrl);

        const response = await fetch(fullUrl);
        const blob = await response.blob();
        const base64 = await convertBase64(blob);

        setSelectedFileUrl(file.url);
        setImageUrl(base64);
        setSelectedFileForStorage(null);
      } catch (error) {
        console.error("Error details:", error);
        toastMessage({
          message: "Failed to load file from gallery.",
        });
      }
    } else {
      setSelectedFileUrl("");
      setImageUrl("");
    }
  };

  return (
    <Dialog open={open} maxWidth="lg" fullWidth onClose={() => setOpen(false)}>
      <DialogTitle
        className="shadow-md"
        style={{
          backgroundColor: currentMode === "Dark" ? "#212B36" : "#fff",
          color: currentMode === "Dark" ? "white" : "black",
        }}>
        Add Step
      </DialogTitle>
      <DialogContent
        style={{
          backgroundColor: currentMode === "Dark" ? "#212B36" : "#fff",
          color: currentMode === "Dark" ? "white" : "black",
        }}>
        <form>
          <InputLabel style={{marginBottom: "10px"}}>Step Title *</InputLabel>
          <TextField
            onChange={e => setTitle(e.target.value)}
            value={title}
            onBlur={() => setTitle(title.trim())}
            style={{marginBottom: "10px"}}
            variant="outlined"
            fullWidth
            required
          />
          {(steps?.length ?? 0) > 0 && (
            <Box sx={{mb: 2}}>
              <InputLabel style={{marginBottom: "10px"}}>
                Step position (1 = top, {(steps?.length ?? 0) + 1} = end) *
              </InputLabel>
              <FormControl fullWidth required>
                <TextField
                  label="Step position"
                  type="number"
                  inputProps={{
                    min: 1,
                    max: (steps?.length ?? 0) + 1,
                    step: 1,
                  }}
                  value={insertIndex}
                  onChange={e => {
                    const val = e.target.value;
                    if (
                      val === "" ||
                      (/^\d+$/.test(val) &&
                        Number(val) >= 1 &&
                        Number(val) <= (steps?.length ?? 0) + 1)
                    ) {
                      setInsertIndex(val);
                    }
                  }}
                  required
                  fullWidth
                  error={
                    insertIndex === "" ||
                    Number(insertIndex) < 1 ||
                    Number(insertIndex) > (steps?.length ?? 0) + 1
                  }
                  helperText={
                    insertIndex === ""
                      ? "Please enter a position"
                      : Number(insertIndex) < 1 ||
                        Number(insertIndex) > (steps?.length ?? 0) + 1
                      ? `Position must be between 1 and ${
                          (steps?.length ?? 0) + 1
                        }`
                      : ""
                  }
                  select
                  SelectProps={{
                    native: false,
                    renderValue: selected =>
                      selected || "Select or enter position",
                    MenuProps: {
                      PaperProps: {
                        style: {
                          backgroundColor:
                            currentMode === "Dark" ? "#161C24" : "#fff",
                        },
                      },
                    },
                  }}>
                  <MenuItem value="">Select position</MenuItem>
                  {[...Array((steps?.length ?? 0) + 1)].map((_, idx) => (
                    <MenuItem key={idx + 1} value={String(idx + 1)}>
                      {idx + 1}{" "}
                      {idx + 1 === (steps?.length ?? 0) + 1 ? "(End)" : ""}
                    </MenuItem>
                  ))}
                </TextField>
              </FormControl>
            </Box>
          )}
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "flex-start",
              justifyContent: "space-between",
              width: "inherit",
            }}>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                width: "48%",
                alignSelf: "flex-start",
              }}>
              <InputLabel style={{marginBottom: "10px"}}>
                Sensor Value
              </InputLabel>
              <section className="flex">
                <Box sx={{width: "100%"}}>
                  <FormControl fullWidth>
                    <Select
                      value={sensorValue}
                      onChange={e => setSensorValue(e.target.value)}>
                      {sensorList?.map(mData => (
                        <MenuItem
                          key={mData._id}
                          value={mData._id}
                          sx={menuItemTheme}>
                          {mData.tag} {mData.value ? `(${mData.value})` : ""}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
                <IconButton
                  className="bg-red-700"
                  onClick={() =>
                    sensorValue
                      ? setSensor([...sensor, sensorValue])
                      : toastMessageWarning({message: "Missing sensor value"})
                  }>
                  <AddIcon className="text-green-800" />
                </IconButton>
              </section>
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  flexWrap: "wrap",
                  marginTop: "5px",
                  columnGap: "1rem",
                }}>
                {sensor?.map((sensorId, idx) => {
                  const sensorData = sensorList.find(s => s._id === sensorId);
                  return (
                    <div key={sensorId + idx}>
                      <span className="font-bold">{idx + 1}. </span>
                      {sensorData?.tag || "Unknown"}
                      <IconButton onClick={() => prePopDataSensor(idx)}>
                        <RemoveIcon className="text-red-800" />
                      </IconButton>
                    </div>
                  );
                })}
              </div>
            </div>

            <div
              style={{
                display: "flex",
                flexDirection: "column",
                minWidth: "48%",
                alignSelf: "flex-start",
              }}>
              <InputLabel style={{marginBottom: "10px"}}>Qr</InputLabel>
              <section className="flex">
                <Box sx={{width: "100%"}}>
                  <TextField
                    onChange={e => setQrcodeValue(e.target.value)}
                    value={qrcodeValue}
                    onBlur={() => setQrcodeValue(qrcodeValue.trim())}
                    style={{marginBottom: "10px"}}
                    variant="outlined"
                    fullWidth
                  />
                </Box>
                <IconButton
                  className="bg-red-700"
                  onClick={() =>
                    qrcodeValue
                      ? setQrcodes([...qrcodes, qrcodeValue])
                      : toastMessageWarning({message: "Missing Qr value"})
                  }>
                  <AddIcon className="text-green-800" />
                </IconButton>
              </section>
              <div style={{marginBottom: qrcodes.length ? "20px" : "0px"}}>
                {qrcodes?.map((data, idx) => (
                  <div key={data + idx}>
                    <span className="font-bold">{idx + 1}. </span> {data}{" "}
                    <IconButton onClick={() => prePopDataQr(idx)}>
                      <RemoveIcon className="text-red-700" />
                    </IconButton>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <InputLabel style={{marginBottom: "10px"}}>
            Step Description *
          </InputLabel>
          <TextField
            onChange={e => setDesc(e.target.value)}
            value={desc}
            onBlur={() => setDesc(desc.trim())}
            style={{marginBottom: "10px"}}
            variant="outlined"
            fullWidth
            multiline
            rows={2}
            required
          />
          <InputLabel style={{marginBottom: "10px"}}>Answer</InputLabel>
          <TextField
            onChange={e => setAns(e.target.value)}
            value={ans}
            onBlur={() => setAns(ans.trim())}
            style={{marginBottom: "10px"}}
            variant="outlined"
            fullWidth
            multiline
            rows={2}
            required
          />

          <div
            style={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              width: "inherit",
            }}>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                minWidth: "48%",
              }}>
              <InputLabel style={{marginBottom: "10px"}}>Format *</InputLabel>
              <FormControl
                style={{marginBottom: "10px"}}
                required
                fullWidth
                variant="outlined">
                <Select onChange={e => setFormat(e.target.value)}>
                  <MenuItem value="image" sx={menuItemTheme}>
                    Image
                  </MenuItem>
                  <MenuItem value="video" sx={menuItemTheme}>
                    Video
                  </MenuItem>
                  <MenuItem value="audio" sx={menuItemTheme}>
                    Audio
                  </MenuItem>
                  <MenuItem value="text" sx={menuItemTheme}>
                    Text
                  </MenuItem>
                </Select>
              </FormControl>
            </div>

            <div
              style={{
                display: "flex",
                flexDirection: "column",
                minWidth: "48%",
              }}>
              <InputLabel style={{marginBottom: "10px"}}>Type *</InputLabel>
              <FormControl
                style={{marginBottom: "10px"}}
                required
                fullWidth
                variant="outlined">
                <Select onChange={e => setType(e.target.value)}>
                  <MenuItem value="info" sx={menuItemTheme}>
                    Info
                  </MenuItem>
                  <MenuItem value="camera" sx={menuItemTheme}>
                    Camera
                  </MenuItem>
                  <MenuItem value="critical" sx={menuItemTheme}>
                    Critical
                  </MenuItem>
                  <MenuItem value="normal" sx={menuItemTheme}>
                    Normal
                  </MenuItem>
                </Select>
              </FormControl>
            </div>
          </div>

          {format && format !== "text" && (
            <>
              <InputLabel style={{marginBottom: "10px"}}>
                Media Source
              </InputLabel>
              <RadioGroup
                row
                value={mediaSource}
                onChange={e => {
                  setMediaSource(e.target.value);
                  setImageUrl("");
                  setSelectedFileForStorage(null);
                  setSelectedFileUrl("");
                }}
                style={{marginBottom: "10px"}}>
                <FormControlLabel
                  value="upload"
                  control={<Radio />}
                  label="Upload New File"
                />
                <FormControlLabel
                  value="fileManager"
                  control={<Radio />}
                  label="Select from File Manager"
                />
              </RadioGroup>

              {mediaSource === "upload" && (
                <>
                  <InputLabel style={{marginBottom: "10px"}}>
                    Upload Media
                  </InputLabel>
                  <DropzoneArea
                    showFileNames
                    onChange={handleChange}
                    dropzoneText="Drag and Drop / Click to ADD Media"
                    showAlerts={false}
                    filesLimit={1}
                    maxFileSize={50 * 1024 * 1024}
                    onDelete={handleDeleteMedia}
                    onAlert={message => {
                      if (message.includes("File is too big")) {
                        toastMessage({
                          message:
                            "File size exceeds 50MB. Please upload a smaller file.",
                        });
                      }
                    }}
                  />
                </>
              )}

              {mediaSource === "fileManager" && (
                <>
                  <InputLabel style={{marginBottom: "10px"}}>
                    Select File from File Manager
                  </InputLabel>
                  <Box display="flex" gap={2} alignItems="center" marginBottom={2}>
                    <Button
                      variant="outlined"
                      onClick={() => setGalleryModalOpen(true)}
                      disabled={!format}
                      fullWidth
                      style={{
                        padding: "12px",
                        textTransform: "none",
                        justifyContent: "flex-start",
                      }}
                    >
                      {selectedFileUrl ? (
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography>
                            {fileManagerFiles.find(f => f.url === selectedFileUrl)?.name || "Selected file"}
                          </Typography>
                        </Box>
                      ) : (
                        <Typography color="textSecondary">
                          Click to browse gallery
                        </Typography>
                      )}
                    </Button>
                    {selectedFileUrl && (
                      <IconButton 
                        onClick={() => {
                          setSelectedFileUrl("");
                          setImageUrl("");
                        }}
                        color="error"
                      >
                        <CloseIcon />
                      </IconButton>
                    )}
                  </Box>
                  
                  {!format && (
                    <Typography variant="caption" color="error" style={{ marginBottom: "10px" }}>
                      Please select a format first to browse files
                    </Typography>
                  )}
                </>
              )}

              <div style={{display: "flex", justifyContent: "center"}}>
                {imageUrl ? (
                  <GetPreviewComponent
                    sourceUrl={imageUrl}
                    fileFormat={format}
                    previewImageStyle={{width: "450px", marginTop: "20px"}}
                    previewVideoStyle={{width: "75%", marginTop: "20px"}}
                    previewAudioStyle={{
                      marginTop: "15%",
                      marginRight: "50px",
                      marginBottom: "20px",
                    }}
                  />
                ) : (
                  <img src="data:image/png;base64,..." alt="img" />
                )}
              </div>
            </>
          )}
          {parent === "Maintenance" && (
            <>
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-between",
                }}>
                <div style={{width: "50%"}}>
                  <InputLabel style={{marginBottom: "10px"}}>
                    Hashtag
                  </InputLabel>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      width: "100%",
                    }}>
                    <Autocomplete
                      style={{width: "90%"}}
                      value={findBy}
                      onChange={(event, newValue) => {
                        setFindBy(newValue || "");
                        sethashdata(newValue || "");
                      }}
                      inputValue={inputFindBy}
                      onInputChange={(event, newInputValue) =>
                        handleChanges(newInputValue)
                      }
                      id="controllable-states-demo"
                      options={hashArray}
                      renderOption={(props, option) => (
                        <span
                          {...props}
                          style={{
                            background:
                              currentMode === "Dark" ? "#161C24" : "#fff",
                            color: currentMode === "Dark" ? "white" : "black",
                          }}>
                          {option}
                        </span>
                      )}
                      sx={{width: 400}}
                      renderInput={params => (
                        <TextField
                          {...params}
                          inputRef={searchInput}
                          label="Search"
                          onChange={e => setHashtag(e.target.value)}
                        />
                      )}
                    />
                    <IconButton
                      onClick={handleButtonClickOld}
                      style={{marginBottom: "10px"}}>
                      <AddIcon className="text-green-800" />
                    </IconButton>
                  </div>
                </div>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "flex-end",
                    justifyContent: "flex-start",
                    gap: "1rem",
                    minWidth: "48%",
                  }}>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "flex-start",
                      justifyContent: "space-between",
                      minWidth: "70%",
                    }}>
                    <InputLabel style={{marginBottom: "10px"}}>
                      Add Hashtag
                    </InputLabel>
                    <TextField
                      value={hashtag}
                      onChange={e => setHashtag(e.target.value)}
                      variant="outlined"
                      fullWidth
                    />
                  </div>
                  <ButtonBasic
                    buttonTitle="ADD NEW"
                    style={{width: "30%"}}
                    color="secondary"
                    variant="contained"
                    onClick={handleButtonClick}
                  />
                </div>
              </div>
              <ul
                style={{
                  display: "flex",
                  flexDirection: "row",
                  columnGap: "0.5rem",
                  flexWrap: "wrap",
                }}>
                {hashtagArray?.map((item, index) => (
                  <li key={index}>
                    {item}
                    <IconButton onClick={() => removeHashtag(index)}>
                      <RemoveIcon style={{fontSize: "20px", color: "#f00"}} />
                    </IconButton>
                  </li>
                ))}
              </ul>
            </>
          )}
        </form>
      </DialogContent>
      {loading && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 1,
          }}>
          <CircularProgressWithLabel size={60} value={uploadProgress} />
        </Box>
      )}
      <DialogActions
        style={{
          display: "flex",
          justifyContent: "space-between",
          padding: "0px 36px 24px 36px",
          backgroundColor: currentMode === "Dark" ? "#212B36" : "#fff",
        }}>
        <ButtonBasic
          buttonTitle="Cancel"
          width="30%"
          color="error"
          variant="contained"
          onClick={handleClose}
        />
        {title.trim() === "" ||
        type === "" ||
        format === "" ||
        desc.trim() === "" ||
        (format !== "text" &&
          mediaSource === "upload" &&
          !selectedFileForStorage) ||
        (format !== "text" &&
          mediaSource === "fileManager" &&
          !selectedFileUrl) ||
        insertIndex === "" ||
        Number(insertIndex) < 1 ||
        Number(insertIndex) > (steps?.length ?? 0) + 1 ? (
          <ButtonBasic
            buttonTitle="Add Step"
            width="30%"
            color="primary"
            variant="contained"
            disabled
            onClick={handleSubmit}
          />
        ) : (
          <ButtonBasic
            buttonTitle="Add Step"
            width="30%"
            color="primary"
            variant="contained"
            onClick={handleSubmit}
          />
        )}
      </DialogActions>

      <GalleryModal
        open={galleryModalOpen}
        onClose={() => setGalleryModalOpen(false)}
        onSelectFile={handleFileManagerSelect}
        files={fileManagerFiles}
        format={format}
        currentMode={currentMode}
      />
    </Dialog>
  );
};

export default AddStepDialog;

export function CircularProgressWithLabel({value, ...props}) {
  return (
    <Box sx={{position: "relative", display: "inline-flex"}}>
      <CircularProgress {...props} />
      <Box
        sx={{
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          position: "absolute",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}>
        <Typography variant="caption" component="div" sx={{color: "#fff"}}>
          {`${Math.round(value)}%`}
        </Typography>
      </Box>
    </Box>
  );
}
